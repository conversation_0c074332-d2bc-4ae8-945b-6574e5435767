import random
from typing import List
import hashlib
import socket
import uuid
import secrets
import sys
import argparse

class SubAccountGenerator:
    """
    随机子账户生成器
    
    根据主账户部分生成带有随机州和城市的子账户
    支持三种格式:
    1. {base}-st-{state}-city-{city}
    2. {base}-city-{city}
    3. {base}-st-{state}
    
    规则:
    - 直接使用提供的州名和城市名（无需额外处理）
    
    示例:
    >>> generator = SubAccountGenerator("admin_w1-zone-resi-region-us")
    >>> generator.generate()
    'admin_w1-zone-resi-region-us-st-california-city-losangeles'  # 可能返回三种格式中的任意一种
    """
    
    def __init__(self, base_account: str, password: str = "WorldUsers135",
                 host: str = "7e61ce2c5694ec79.nbd.us.ip2world.vip", port: int = 6001):
        """
        初始化子账户生成器

        Args:
            base_account: 主账户部分 (如 "admin_w1-zone-resi-region-us")
            password: 代理密码 (默认: "WorldUsers135")
            host: 代理主机 (默认: "7e61ce2c5694ec79.nbd.us.ip2world.vip")
            port: 代理端口 (默认: 6001)
        """
        self.base_account = base_account
        self.password = password
        self.host = host
        self.port = port
        self.states = [
            "california", "florida", "illinois", "newyork", "georgia", "texas", "alabama",
            "arizona", "arkansas", "colorado", "connecticut", "delaware", "indiana",
            "kentucky", "louisiana", "maryland", "massachusetts", "michigan", "minnesota",
            "mississippi", "missouri", "nebraska", "nevada", "newhampshire", "newjersey",
            "northcarolina", "ohio", "oklahoma", "oregon", "pennsylvania", "southcarolina",
            "tennessee", "virginia", "washington", "westvirginia", "wisconsin",
            "districtofcolumbia", "iowa", "kansas", "newmexico", "rhodeisland", "maine",
            "southdakota", "utah", "hawaii", "northdakota", "montana", 
            "alaska", "idaho", "wyoming", "vermont"
        ]
        self.cities = [
            "newyorkcity", "atlanta", "baltimore", "birmingham", "charlotte", "chicago",
            "coloradosprings", "columbus", "dallas", "denver", "doral", "houston",
            "indianapolis", "jacksonville", "kansascity", "lasvegas", "losangeles", "miami",
            "milwaukee", "minneapolis", "orlando", "palmbay", "philadelphia", "phoenix",
            "pittsburgh", "sanantonio", "sandiego", "sanfrancisco", "sanjose", "seattle",
            "burbank", "hiddenhills", "nashville", "ranchosantamargarita", "santamonica",
            "sterlingheights", "santaclara", "akron", "albuquerque", "ashburn", "batonrouge",
            "bowlinggreen", "brandon", "capecoral", "charleston", "cleveland", "fortworth",
            "fresno", "germantown", "hanford", "louisville", "lumberton", "memphis", "mobile",
            "norfolk", "oklahomacity", "pensacola", "portarthur", "providence", "raleigh",
            "riverside", "sacramento", "st.louis", "tampa", "washington", "waterbury",
            "wichita", "winstonsalem", "albany", "augusta", "boston", "cincinnati", "elpaso",
            "fayetteville", "neworleans", "oakland", "yonkers", "aurora", "austin", "detroit",
            "lamirada", "pompanobeach", "toledo", "allen", "kissimmee", "bakersfield",
            "clevelandheights", "danville", "fairlawn", "flint", "laredo", "macon", "oaklawn",
            "rochester", "saltlakecity", "sikeston", "stockbridge", "syracuse", "fallon",
            "buffalo", "carson", "clovis", "dayton", "hollywood", "jackson", "arlington",
            "belleroseterrace", "brooksville", "longbeach", "portsaintlucie", "eastprovidence",
            "florissant", "scranton", "casasadobes", "deltona", "greenacrescity", "knoxville",
            "newark", "spartanburg", "stockton", "tallahassee", "northbergen", "secaucus",
            "boardman", "orem", "wilmington", "turlock", "eastlosangeles", "anaheim", "antioch",
            "azusa", "belair", "bellflower", "bluffton", "boise", "brea", "butte", "clarksville",
            "compton", "corona", "covina", "downersgrove", "elkgrove", "fairfield", "fontana",
            "fremont", "gardena", "gardengrove", "gary", "glendora", "greenbay", "haciendaheights",
            "hemet", "hercules", "hesperia", "highland", "huntingtonbeach", "indio", "inglewood",
            "jeffersonville", "lakeelsinore", "laporte", "lexington", "lima", "livermore", "manteca",
            "missionhills", "modesto", "montereypark", "morenovalley", "murrieta", "norwalk",
            "oceanside", "ontario", "oxnard", "palmdale", "perris", "portage", "porterville",
            "racine", "ranchopalosverdes", "reno", "richmond", "rocksprings", "roseville",
            "sanbernardino", "sanbruno", "sanleandro", "santaana", "santaclarita", "santarosa",
            "shinglesprings", "siouxfalls", "st.petersburg", "sterling", "tacoma", "temecula",
            "torrance", "tracy", "unioncity", "vallejo", "westsacramento", "whittier", "bellwood",
            "billings", "cheyenne", "hammond", "hardeeville", "hawthorne", "hayward", "inwood",
            "lafayette", "milton", "monrovia", "nampa", "novato", "omaha", "orlandpark", "pasadena",
            "pittsburg", "ranchocucamonga", "tulsa", "utica", "vacaville", "visalia", "westcovina",
            "yakima", "appleton", "casper", "chulavista", "conroe", "delaware", "elmhurst",
            "fortlauderdale", "lexingtonfayette", "madera", "manchester", "menifee", "palmcoast",
            "pomona", "radcliff", "ranchocordova", "redwoodcity", "saintgeorge", "saintjohn",
            "sandusky", "tomsriver", "tulare", "valparaiso", "yucaipa", "brentwood", "chesapeake",
            "columbia", "eastranchodominguez", "edwardsville", "hartford", "hobart", "lakewood",
            "lombard", "michigancity", "newbedford", "portland", "quincy", "suffolk", "worcester",
            "bardstown", "belleville", "bullheadcity", "castrovalley", "citrusheights", "corpuschristi",
            "cypress", "dalycity", "huntsville", "joliet", "lakehavasucity", "monroe", "oakley",
            "peoria", "rockford", "simivalley", "waldorf", "berkeley", "hagerstown", "lamont",
            "saintpaul", "sanmateo", "victorville", "yubacity", "bolingbrook", "bowie", "cerritos",
            "concord", "crownpoint", "essex", "frenchcamp", "hannibal", "killeen", "sunnyside",
            "wesleychapel", "carmichael", "dover", "jerseyville", "lodi", "mesa", "summit", "twinfalls",
            "waukegan", "idahofalls", "merrillville", "paloalto", "vancouver", "arlingtonheights",
            "clifton", "hainescity", "newalbany", "sunnyvale", "tinleypark", "uniongrove", "westbend",
            "edison", "hialeah", "trenton", "grandview", "leesburg", "elizabeth", "greensboro", "medina",
            "adel", "aiken", "allentown", "altoona", "amarillo", "bear", "beaumont", "bellevue", "bessemer",
            "bethlehem", "blackshear", "bocaraton", "bristol", "brockton", "burlington", "cammackvillage",
            "cedarrapids", "centreville", "champaign", "chattanooga", "cocoa", "dalton", "dearborn",
            "deland", "denton", "desmoines", "desoto", "draper", "dubuque", "durham", "easthampton",
            "edinburg", "elkton", "ellenton", "elmont", "erie", "evansville", "everett", "fallschurch",
            "fargo", "federalway", "fortwayne", "frederick", "gainesville", "garfieldheights", "garland",
            "garner", "greenwood", "hickory", "highlandsranch", "highpoint", "honolulu", "hoover",
            "jerseycity", "jupiter", "kettering", "lakeland", "lansing", "largo", "lehighacres",
            "levittown", "lewisville", "lincoln", "linden", "lubbock", "massapequa", "massillon",
            "mclean", "melbourne", "middletown", "montgomery", "mountvernon", "muskegon", "myrtlebeach",
            "newhaven", "newnan", "northolmsted", "oakville", "ocala", "overland", "oviedo", "ozark",
            "palmerheights", "pawtucket", "pharr", "pikesville", "pikeville", "raeford", "rockhill",
            "roundrock", "saginaw", "saintaugustine", "sanluisobispo", "santafe", "slidell", "smyrna",
            "southbend", "southriver", "spokanevalley", "spring", "springfield", "springhill", "summerville",
            "thomasville", "troy", "tucson", "tyler", "valdosta", "valrico", "verobeach",
            "virginiabeach", "waco", "warren", "wausau", "westhaven", "westland", "wichitafalls", "winterset",
            "woodlake", "ninetysix", "altamontesprings", "anchorage", "anderson", "beaverton", "bridgeport",
            "cairo", "canton", "carrollton", "centereach", "chicopee", "clearwater", "clermont", "clintontownship",
            "commack", "conway", "coosbay", "coralhills", "crescentsprings", "danbury", "elkhorn", "fishers",
            "fortpierce", "fredericksburg", "fuquayvarina", "gaithersburg", "gardendale", "gilbert", "goldsboro",
            "greenville", "gresham", "hagaman", "hamilton", "hillsborough", "irving", "jonesboro", "leeds",
            "lillington", "littlerock", "lutz", "miamigardens", "midland", "newbern", "newportnews",
            "newportrichey", "newtown", "ocoee", "ormondbeach", "owensboro", "plano", "plantation", "portsmouth",
            "revere", "roanoke", "rochesterhills", "rockymount", "saintpeters", "sanford", "savannah", "sayreville",
            "shallotte", "sherman", "shirley", "southoldbridge", "statesville", "stroudsburg", "suwanee",
            "texarkana", "tuckahoe", "victoria", "westpalmbeach", "wilson", "woodside", "youngstown", "bend",
            "bethania", "bloomington", "bradenton", "brownsville", "buckeye", "cambridge", "canyonlake", "carlsbad",
            "cary", "davenport", "davie", "douglasville", "eastorange", "elkhart", "elmira", "fairfax", "florence",
            "fortcollins", "fortmill", "fortwashington", "gastonia", "gulfport", "hackensack", "harrisburg",
            "homestead", "hyattsville", "irvine", "johnson", "lithonia", "lorain", "lowell", "maryville", "medford",
            "merced", "mesquite", "miamibeach", "milford", "mishawaka", "murfreesboro", "murrellsinlet", "odessa",
            "olathe", "prospectpark", "shelby", "simpsonville", "southbridge", "spokane", "springlake", "stratford",
            "thewoodlands", "universalcity", "universitygardens", "waterford", "weatherford", "westplains",
            "ypsilanti", "zephyrhills", "rockville", "floris", "phenixcity", "seatac", "talladega", "abilene",
            "amherst", "apopka", "atascadero", "bellingham", "blountville", "boyntonbeach", "brumley", "burke",
            "commercecity", "coralsprings", "delraybeach", "dixhills", "eastpeoria", "fairport", "flowermound",
            "franklin", "graham", "greeneville", "hendersonville", "katy", "lacey", "lakesaintlouis", "lawrenceville",
            "lebanon", "lintonhall", "littleriver", "livonia", "madison", "marietta", "mason", "mckenzie", "mckinney",
            "meridian", "monckscorner", "noblesville", "norman", "northlittlerock", "plantcity", "prescott", "redlands",
            "richardson", "roseburg", "rosenberg", "royaloak", "shreveport", "soddydaisy", "strongsville", "surprise",
            "thornton", "urbandale", "wimauma", "alpharetta", "batavia", "biloxi", "bismarck", "bloomfield", "bossiercity",
            "bothell", "bryan", "carmel", "davison", "decatur", "deerfieldbeach", "edmond", "elgin", "fairoaks", "fairview",
            "fonddulac", "forney", "fortlee", "frisco", "gilroy", "glenburnie", "hicksville", "houma", "johnsoncity",
            "kalamazoo", "linwood", "luray", "manvel", "mentor", "metairie", "newbraunfels", "palmbeachgardens", "palmharbor",
            "pflugerville", "pontiac", "pueblo", "rockwall", "saintcharles", "sammamish", "southhill", "sugarhill", "tamarac",
            "tangipahoa", "traversecity", "wakeforest", "westbabylon", "weymouth", "winterpark", "wylie", "wyoming", "yuma",
            "alexandria", "annarbor", "athens", "auburn", "benton", "castlerock", "cedarburg", "cheektowaga", "cumming",
            "dalecity", "eauclaire", "farmington", "glendale", "grantspass", "keystone", "kokomo", "longmont", "longneck",
            "middleville", "olivebranch", "oxford", "pembrokepines", "pineville", "redmond", "rockingham", "rosemount", "salem",
            "sumter", "titusville", "trinity", "villageofcamptonhills", "wasilla", "wintergarden", "woodstock", "worthington",
            "wyandotte", "naperville", "anacortes", "asheville", "burien", "california", "celina", "drexelhill", "edgewater", 
            "forestbrook", "grandjunction", "grandrapids", "hollyridge", "kernersville", "lawton", "lynn", "martinsburg", 
            "mchenry", "missoula", "morristown", "newbaltimore", "oswego", "panamacity", "parker", "prairieville", "reston", 
            "riorancho", "ruskin", "sanangelo", "snohomish", "thevillages", "ventura", "wellington", "westjordan", "apex", 
             "doylestown", "eldersburg", "georgetown", "lakemonticello", "longview", "madisonville", "marrero", 
            "mooresville", "newbritain", "overlandpark", "palmer", "saintcloud", "salisbury", "sebastian", "sicklerville", 
            "siouxcity", "snellville", "sunprairie", "tupelo", "waxahachie", "york", "elkgrovevillage", "mcallen", "waterloo", 
            "bastrop", "bethesda", "covington", "crofton", "euless", "folcroft", "gettysburg", "greatfalls", 
            "hiltonheadisland", "hinton", "hondo", "hopewell", "kingofprussia", "lakeridge", "libertyville", "lorton",
            "matthews", "mechanicsville", "millsboro", "montrose", "newmilford", "niagarafalls", "plumsteadville", 
            "progress", "stow", "travilah", "woodlyn", "wrightsville", "shelton", "universityheights", "belleplaine", 
            "jeffersonheights", "dickson", "aloha", "baycity", "blaine", "brunswick", "cedarpark", "chester", 
            "denhamsprings", "dundalk", "granitebay", "greenfield", "irmo", "kingsmountain", "lackawanna", "littleelm",
            "montevallo", "northlasvegas", "northmiami", "odenton", "opelika", "portorange", "riverview", "springvalley", 
            "topeka", "waterville", "weaverville", "brevard", "collegestation", "coram", "costamesa", "daytonabeach", 
            "fairbanks", "flagstaff", "forestville", "huntersville", "northcanton", "onalaska", "oregon", "palmsprings", 
            "stamford", "vincennes", "westfield", "westlakevillage", "westtorrington", "wildwood", "camden", "cookeville", 
            "greeley", "lafollette", "middleriver", "minot", "negaunee", "newburgh", "palmdesert", "prosper", 
            "schenectady", "taylor", "thibodaux", "watertown", "aquiaharbour", "bayshore", "belleview", "chandler", 
            "clarksburg", "dothan", "glastonburycenter", "gloversville", "grovecity", "hudson", "keller", "kencaryl",
            "mcdonough", "nassaubay", "pace", "potomac", "roswell", "westville", "zanesville", "amelia", "bremerton", 
            "cuyahogafalls", "eldoradohills", "enid", "ephrata", "fayette", "huntvalley", "martinsville",
            "meriden", "middleburg", "nortonshores", "parkland", "plymouth", "sanmarcos", "southernpines", "union", 
            "waynesboro", "westorange", "wintersprings", "yorkville", "cassopolis", "crossville", "dandridge", 
            "easton", "grandprairie", "hartsville", "margate", "pahrump", "paterson", "reidsville", "stonemountain", 
            "temple", "tifton", "anniston", "atascocita", "auburndale", "avon", "carlisle", "chillicothe", "clinton", 
            "kannapolis", "kingman", "lakeport", "malden", "newphiladelphia", "ocilla", "plainfield", "wilkesbarre", 
            "yorktownheights", "bayonne", "camilla", "garfield", "hempstead", "laurinburg", "mansfield", "randallstown",
            "carbondale", "hays", "lufkin", "maricopa", "mechanicsburg", "mission", "newcity", "orangeburg", "taunton", 
            "westerville", "beltsville", "berlin", "binghamton", "blueisland", "eustis", "geneva", "oakforest", "weslaco", 
            "baxley", "brazil", "brooklynpark", "delano", "dunnellon", "elizabethcity", "hahira", "moultrie", "newkensington", 
            "oakvalley", "algonquin", "grandblanc", "harkerheights", "wooster", "citruspark", "libertylake", "branford", 
            "cedarhill", "cicero", "escalon", "guntersville", "kyle", "laurel", "millwood", "paducah", "santamaria", 
            "seminole", "severnapark", "sevierville", "southlake", "alcoa", "ashland", "buxton", "cranston", "downingtown", 
            "grandforks", "gurnee", "keizer", "orangepark", "tamiami", "terrehaute", "thepinehills", "vestal", "wheaton", 
            "lakeside", "sugarland", "wrens", "ames", "ankeny", "canonsburg", "carsoncity", "clarkssummit", "duluth", "duncan",
            "easthartford", "fairhope", "jeffersoncity", "kirkland", "lacrosse", "lancaster", "lynnwood", "moseslake", 
            "naples", "owatonna", "petersburg", "rocklin", "shawnee", "themeadows", "troutville", "winterhaven", "bartlesville", 
            "camarillo", "chardon", "dekalb", "hotsprings", "johnstown", "kalispell", "lagrange", "lakecharles", "leominster", 
            "lynchburg", "mountairy", "newcastle", "northfortmyers", "oregoncity", "ruston", "saintjoseph", "uniontown", "whitmorelake",
             "aspenhill", "carrollwoodvillage", "catonsville", "holland", "kent", "lawrence", "lenexa", "lompoc", "marysville", "ojai", 
            "orangebeach", "peekskill", "puyallup", "rowlett", "safetyharbor", "acushnet", "applevalley", "atoka", "aubrey", "battlecreek", 
            "beaufort", "calumetc city", "chino", "congers", "elyria", "fairburn", "gibbsboro", "mcminnville", "miramar", "moline", "natchez",
             "newlondon", "oshkosh", "petoskey", "porthuron", "southfield", "starkville", "trotwood"
        ]

    def _generate_machine_id(self):
        """生成机器标识"""
        # 使用主机名和 MAC 地址
        hostname = socket.gethostname()
        mac = uuid.getnode()
        return hashlib.sha256(f"{hostname}{mac}".encode()).hexdigest()[:8] 
    
    def generate_session_id(self,  entropy=6):
        return secrets.token_hex(entropy)   
         
    def generate(self,minute=0) -> str:
        """
        生成随机子账户
        
        Returns:
            生成的子账户字符串
        """
        # 随机选择一种形态 (0, 1 或 2)
        form = random.randint(0, 2)
        sessTime = ''
        if minute > 0:
            sessTime = f"-session-{self.generate_session_id()}-sessTime-{minute}"

        if form == 0:
            # 形态1: {base}-st-{state}-city-{city}
            state = random.choice(self.states)
            city = random.choice(self.cities)
            return f"{self.base_account}-city-{city}{sessTime}"
        elif form == 1:
            # 形态2: {base}-city-{city}
            city = random.choice(self.cities)
            return f"{self.base_account}-city-{city}{sessTime}"
        else:
            # 形态3: {base}-st-{state}
            state = random.choice(self.states)
            return f"{self.base_account}-st-{state}{sessTime}"

    def generate_proxy(self, minute=0) -> str:
        """
        生成SOCKS5代理格式的字符串

        Args:
            minute: 会话时间（分钟）

        Returns:
            SOCKS5代理格式字符串，如：socks5://username:password@host:port
        """
        username = self.generate(minute)
        return f"socks5://{username}:{self.password}@{self.host}:{self.port}"

    def generate_multiple_proxies(self, count: int, minute=0) -> List[str]:
        """
        生成多个代理

        Args:
            count: 生成代理的数量
            minute: 会话时间（分钟）

        Returns:
            代理列表
        """
        return [self.generate_proxy(minute) for _ in range(count)]

    def save_proxies_to_file(self, count: int, filename: str = "proxy.txt", minute=0):
        """
        生成代理并保存到文件

        Args:
            count: 生成代理的数量
            filename: 保存的文件名
            minute: 会话时间（分钟）
        """
        proxies = self.generate_multiple_proxies(count, minute)
        with open(filename, 'w', encoding='utf-8') as f:
            for proxy in proxies:
                f.write(proxy + '\n')
        print(f"已生成 {count} 个代理并保存到 {filename}")
        return proxies

# 示例用法
if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='生成SOCKS5代理列表')
    parser.add_argument('count', nargs='?', type=int, default=100,
                       help='生成代理的数量 (默认: 100)')
    parser.add_argument('--base-account', default='admin_w1-zone-resi-region-us',
                       help='基础账户名 (默认: admin_w1-zone-resi-region-us)')
    parser.add_argument('--password', default='WorldUsers135',
                       help='代理密码 (默认: WorldUsers135)')
    parser.add_argument('--host', default='7e61ce2c5694ec79.nbd.us.ip2world.vip',
                       help='代理主机 (默认: 7e61ce2c5694ec79.nbd.us.ip2world.vip)')
    parser.add_argument('--port', type=int, default=6001,
                       help='代理端口 (默认: 6001)')
    parser.add_argument('--output', default='proxy.txt',
                       help='输出文件名 (默认: proxy.txt)')
    parser.add_argument('--minute', type=int, default=0,
                       help='会话时间（分钟） (默认: 0)')

    args = parser.parse_args()

    generator = SubAccountGenerator(
        base_account=args.base_account,
        password=args.password,
        host=args.host,
        port=args.port
    )

    # 生成代理并保存到文件
    proxies = generator.save_proxies_to_file(args.count, args.output, args.minute)

    # 显示前几个示例
    print(f"\n前5个代理示例:")
    for i, proxy in enumerate(proxies[:5], 1):
        print(f"{i}. {proxy}")

    if len(proxies) > 5:
        print(f"... 还有 {len(proxies) - 5} 个代理")

    print(f"\n使用方法:")
    print(f"python SubAccountGenerator.py 50  # 生成50个代理")
    print(f"python SubAccountGenerator.py     # 生成100个代理（默认）")

