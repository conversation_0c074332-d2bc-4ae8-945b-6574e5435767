# SubAccountGenerator - SOCKS5代理生成器

## 功能说明

这个工具可以生成指定格式的SOCKS5代理列表，并保存到文件中。

## 生成格式

生成的代理格式为：
```
socks5://admin_w1-zone-resi-region-us-st-california:<EMAIL>:6001
```

## 使用方法

### 基本用法

```bash
# 生成默认100条代理
python SubAccountGenerator.py

# 生成指定数量的代理
python SubAccountGenerator.py 50

# 生成5条代理
python SubAccountGenerator.py 5
```

### 高级参数

```bash
# 查看所有可用参数
python SubAccountGenerator.py --help

# 自定义参数示例
python SubAccountGenerator.py 20 \
    --base-account "custom-account" \
    --password "MyPassword123" \
    --host "custom.proxy.host" \
    --port 8080 \
    --output "my_proxies.txt" \
    --minute 10
```

### 参数说明

- `count`: 生成代理的数量（位置参数，默认100）
- `--base-account`: 基础账户名（默认：admin_w1-zone-resi-region-us）
- `--password`: 代理密码（默认：WorldUsers135）
- `--host`: 代理主机（默认：7e61ce2c5694ec79.nbd.us.ip2world.vip）
- `--port`: 代理端口（默认：6001）
- `--output`: 输出文件名（默认：proxy.txt）
- `--minute`: 会话时间（分钟，默认：0，不添加会话信息）

## 输出文件

生成的代理会保存到指定的文件中（默认为 `proxy.txt`），每行一个代理。

## 代理格式变化

工具会随机生成三种格式的用户名：
1. `{base}-st-{state}-city-{city}` - 包含州和城市
2. `{base}-city-{city}` - 只包含城市
3. `{base}-st-{state}` - 只包含州

如果设置了会话时间（--minute > 0），会在用户名中添加会话信息：
```
{base}-st-{state}-session-{session_id}-sessTime-{minute}
```

## 示例输出

```
socks5://admin_w1-zone-resi-region-us-city-losangeles:<EMAIL>:6001
socks5://admin_w1-zone-resi-region-us-st-california:<EMAIL>:6001
socks5://admin_w1-zone-resi-region-us-st-texas-city-houston:<EMAIL>:6001
```
