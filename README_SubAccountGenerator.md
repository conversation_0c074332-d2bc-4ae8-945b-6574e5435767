# SubAccountGenerator - SOCKS5代理生成器

## 功能说明

这个工具可以生成指定格式的SOCKS5代理列表，并保存到文件中。每次运行都会自动清空原有的代理文件。

## 生成格式

生成的代理格式为：
```
socks5://admin_w1-zone-resi-region-us-st-california:<EMAIL>:6001
```

## 使用方法

### 基本用法（推荐）

```bash
# 直接运行，使用配置文件中的参数
python SubAccountGenerator.py
```

**注意：现在所有参数都通过配置文件 `config.json` 进行设置，无需传递命令行参数。**

## 配置文件

所有参数都通过 `config.json` 文件进行配置：

```json
{
    "count": 100,
    "base_account": "admin_w1-zone-resi-region-us",
    "password": "WorldUsers135",
    "host": "7e61ce2c5694ec79.nbd.us.ip2world.vip",
    "port": 6001,
    "output_file": "proxy.txt",
    "minute": 0
}
```

### 配置参数说明

- `count`: 生成代理的数量（默认：100）
- `base_account`: 基础账户名（默认：admin_w1-zone-resi-region-us）
- `password`: 代理密码（默认：WorldUsers135）
- `host`: 代理主机（默认：7e61ce2c5694ec79.nbd.us.ip2world.vip）
- `port`: 代理端口（默认：6001）
- `output_file`: 输出文件名（默认：proxy.txt）
- `minute`: 会话时间（分钟，默认：0，不添加会话信息）

### 修改配置

1. 编辑 `config.json` 文件
2. 修改需要的参数值
3. 保存文件
4. 运行 `python SubAccountGenerator.py`

## 输出文件

生成的代理会保存到指定的文件中（默认为 `proxy.txt`），每行一个代理。

**重要：每次运行都会自动清空原有文件，然后写入新的代理数据。**

## 代理格式变化

工具会随机生成三种格式的用户名：
1. `{base}-st-{state}-city-{city}` - 包含州和城市
2. `{base}-city-{city}` - 只包含城市
3. `{base}-st-{state}` - 只包含州

如果设置了会话时间（--minute > 0），会在用户名中添加会话信息：
```
{base}-st-{state}-session-{session_id}-sessTime-{minute}
```

## 示例输出

```
socks5://admin_w1-zone-resi-region-us-city-losangeles:<EMAIL>:6001
socks5://admin_w1-zone-resi-region-us-st-california:<EMAIL>:6001
socks5://admin_w1-zone-resi-region-us-st-texas-city-houston:<EMAIL>:6001
```
