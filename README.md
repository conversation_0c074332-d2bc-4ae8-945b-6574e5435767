# JSON to CSV Parser

这个Python脚本用于解析`data/json/`目录下的JSON文件，提取`hmeEmails`字段并生成CSV文件。

## 功能特性

- 自动解析`data/json/`目录下的JSON文件
- 提取JSON文件中的`result.hmeEmails`字段
- 生成对应的CSV文件到`data/csv/`目录
- 生成总的CSV文件(`total.csv`)，包含所有处理文件的数据
- 支持通过`.env`文件配置要处理的特定文件
- 如果CSV文件已存在，会自动覆盖
- 自动创建输出目录

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

### 1. 处理所有JSON文件

如果`.env`文件中没有配置`FILE_NAME`，脚本会处理`data/json/`目录下的所有JSON文件：

```bash
python parse_json_to_csv.py
```

### 2. 处理指定的JSON文件

在`.env`文件中配置`FILE_NAME`参数：

```env
# 处理单个文件
FILE_NAME=<EMAIL>

# 处理多个文件（用逗号分隔）
FILE_NAME=<EMAIL>,another_file,third_file
```

然后运行脚本：

```bash
python parse_json_to_csv.py
```

## 文件结构

```
.
├── data/
│   ├── json/           # 输入的JSON文件目录
│   │   └── *.json
│   └── csv/            # 输出的CSV文件目录
│       ├── *.csv       # 各个JSON文件对应的CSV文件
│       └── total.csv   # 包含所有数据的总CSV文件
├── .env                # 配置文件
├── parse_json_to_csv.py # 主脚本
├── requirements.txt    # Python依赖
└── README.md          # 说明文档
```

## JSON文件格式要求

脚本期望JSON文件具有以下结构：

```json
{
    "success": true,
    "timestamp": 1752215314,
    "result": {
        "hmeEmails": [
            {
                "origin": "ON_DEMAND",
                "anonymousId": "w29w57dwk90314",
                "domain": "",
                "forwardToEmail": "<EMAIL>",
                "hme": "<EMAIL>",
                "label": "asdasdas",
                "note": "",
                "createTimestamp": 1752044434006,
                "isActive": true,
                "recipientMailId": ""
            }
        ]
    }
}
```

## 输出CSV格式

脚本会生成以下CSV文件：

1. **单个文件的CSV**: 每个JSON文件对应一个CSV文件，文件名与原JSON文件相同
2. **总的CSV文件**: `total.csv`包含所有处理文件的合并数据

生成的CSV文件包含`hmeEmails`数组中所有对象的字段，例如：

- anonymousId
- createTimestamp
- domain
- forwardToEmail
- hme
- isActive
- label
- note
- origin
- recipientMailId

## 错误处理

脚本包含完善的错误处理机制：

- 文件不存在时会显示错误信息
- JSON格式错误时会显示解析错误
- 缺少必要字段时会显示警告
- 写入CSV失败时会显示错误信息

## 注意事项

1. 确保`data/json/`目录存在且包含有效的JSON文件
2. 脚本会自动创建`data/csv/`目录
3. 如果CSV文件已存在，会被覆盖
4. 文件名配置时可以省略`.json`扩展名，脚本会自动添加
